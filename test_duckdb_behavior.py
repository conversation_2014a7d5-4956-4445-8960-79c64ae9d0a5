#!/usr/bin/env python3
"""Test script to check DuckDB's built-in JSON reader behavior for comparison."""

import duckdb
import json
import tempfile
import os

def test_duckdb_json_behavior():
    """Test DuckDB's built-in JSON reader behavior for edge cases."""
    
    # Test 1: Empty object
    print("=== Test 1: Empty Object ===")
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump({}, f)
        empty_obj_file = f.name
    
    try:
        conn = duckdb.connect()
        result = conn.execute(f"SELECT * FROM read_json_auto('{empty_obj_file}')").fetchall()
        columns = conn.execute(f"DESCRIBE SELECT * FROM read_json_auto('{empty_obj_file}')").fetchall()
        print(f"Empty object result: {result}")
        print(f"Empty object columns: {columns}")
    except Exception as e:
        print(f"Empty object error: {e}")
    finally:
        os.unlink(empty_obj_file)
    
    # Test 2: Object with arrays
    print("\n=== Test 2: Object with Arrays ===")
    data = {
        "numbers": [1, 2, 3, 4.5],
        "strings": ["a", "b", "c"],
        "booleans": [True, False, True]
    }
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(data, f)
        obj_arrays_file = f.name
    
    try:
        result = conn.execute(f"SELECT * FROM read_json_auto('{obj_arrays_file}')").fetchall()
        columns = conn.execute(f"DESCRIBE SELECT * FROM read_json_auto('{obj_arrays_file}')").fetchall()
        print(f"Object with arrays result: {result}")
        print(f"Object with arrays columns: {columns}")
    except Exception as e:
        print(f"Object with arrays error: {e}")
    finally:
        os.unlink(obj_arrays_file)
    
    # Test 3: Simple object (for comparison)
    print("\n=== Test 3: Simple Object ===")
    simple_data = {
        "string_field": "test_value",
        "number_field": 42.5,
        "boolean_field": True,
        "null_field": None
    }
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(simple_data, f)
        simple_obj_file = f.name
    
    try:
        result = conn.execute(f"SELECT * FROM read_json_auto('{simple_obj_file}')").fetchall()
        columns = conn.execute(f"DESCRIBE SELECT * FROM read_json_auto('{simple_obj_file}')").fetchall()
        print(f"Simple object result: {result}")
        print(f"Simple object columns: {columns}")
    except Exception as e:
        print(f"Simple object error: {e}")
    finally:
        os.unlink(simple_obj_file)

if __name__ == "__main__":
    test_duckdb_json_behavior()
